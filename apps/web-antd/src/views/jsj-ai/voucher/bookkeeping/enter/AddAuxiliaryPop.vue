<script lang="ts" setup>
  import { reactive, ref } from 'vue';

  import { message } from 'ant-design-vue';

  import emitter from './usermitt';

  defineOptions({
    name: 'AddAuxiliaryPop',
  });

  const props = withDefaults(defineProps<Props>(), {
    auxiliaryType: 'customer',
  });

  const emits = defineEmits(['refresh']);

  interface Props {
    auxiliaryType?:
      | 'customer'
      | 'department'
      | 'employee'
      | 'inventory'
      | 'project'
      | 'supplier';
  }

  const visible = ref(false);
  const formRef = ref<any>(null);
  const currentSubject = ref<any>(null); // 存储当前选中的科目信息

  const formState = reactive({
    code: '999999', // 辅助核算编码，默认999999
    name: '', // 辅助核算名称
    type: props.auxiliaryType, // 辅助核算类型
  });

  // 辅助核算类型选项
  const auxiliaryTypeOptions = [
    { label: '客户', value: 'customer' },
    { label: '供应商', value: 'supplier' },
    { label: '员工', value: 'employee' },
    { label: '部门', value: 'department' },
    { label: '项目', value: 'project' },
    { label: '存货', value: 'inventory' },
  ];

  // 辅助核算类型映射（从API类型到组件类型）
  const auxiliaryTypeMap: Record<string, string> = {
    c: 'customer', // 客户
    d: 'department', // 部门
    e: 'employee', // 员工
    i: 'inventory', // 存货
    p: 'project', // 项目
    s: 'supplier', // 供应商
  };

  // 重置表单
  const resetForm = () => {
    formState.code = '999999';
    formState.name = '';
    formState.type = props.auxiliaryType;
  };

  const open = (
    subjectInfo?: any,
    auxiliaryType?: string,
    llmAuxiliaryValue?: string,
  ) => {
    resetForm();
    currentSubject.value = subjectInfo;

    // 如果传入了辅助核算类型，设置表单的类型
    if (auxiliaryType) {
      // 映射API类型到组件类型
      const mappedType = auxiliaryTypeMap[auxiliaryType] || auxiliaryType;
      if (auxiliaryTypeOptions.some((option) => option.value === mappedType)) {
        formState.type = mappedType as any;
      }
    }

    // 如果有LLM输出的辅助项目值，预填充到名称字段
    if (llmAuxiliaryValue) {
      formState.name = llmAuxiliaryValue;
      console.log('预填充LLM输出的辅助项目值:', llmAuxiliaryValue);
    }

    visible.value = true;
  };

  const handleOk = async () => {
    try {
      await formRef.value.validate();

      // 构建新辅助核算对象，符合AssistantAccountingItem接口
      const newAuxiliary = {
        accountSetId: 0,
        accountTitleId: null,
        code: formState.code,
        customerId: 0,
        freezeStatus: 'normal',
        fullName: formState.name,
        fullNameExtend: null,
        fullNameWithSpace: null,
        id: Date.now(), // 使用时间戳作为唯一ID
        name: formState.name,
        pinYinInitial: null,
        remark: null,
        repeatCode: 0,
        taxpayerNum: null,
        titles: [],
        type: formState.type,
      };

      message.success('新增辅助核算成功');
      // 通知凭证页面刷新辅助核算数据，并传递科目信息
      emitter.emit('account_voucher_auxiliary_added', {
        auxiliary: newAuxiliary,
        subject: currentSubject.value,
      });
      // 通知父组件刷新
      emits('refresh');
      visible.value = false;
    } catch (error) {
      console.error('新增辅助核算失败:', error);
      message.error('新增辅助核算失败');
    }
  };

  const handleCancel = () => {
    visible.value = false;
  };

  defineExpose({
    open,
  });
</script>

<template>
  <a-modal
    v-model:open="visible"
    title="新增辅助项目"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div>
      <!-- 显示当前科目信息 -->
      <a-alert
        v-if="currentSubject"
        :message="`当前科目: ${currentSubject.fullName || currentSubject.text || currentSubject.label || '未知科目'}`"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <a-form
        :model="formState"
        autocomplete="off"
        ref="formRef"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="编码" name="code">
          <a-input
            v-model:value="formState.code"
            placeholder="默认999999"
            disabled
          />
        </a-form-item>
        <a-form-item
          label="名称"
          name="name"
          :rules="[{ required: true, message: '请输入名称' }]"
        >
          <a-input v-model:value="formState.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item
          label="辅助类别"
          name="type"
          :rules="[{ required: true, message: '请选择辅助类别' }]"
        >
          <a-select
            v-model:value="formState.type"
            placeholder="请选择辅助类别"
            :options="auxiliaryTypeOptions"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<style lang="scss" scoped>
  .btn {
    text-align: center;
  }

  .reminder {
    font-size: 10px;
    color: red;
  }
</style>
